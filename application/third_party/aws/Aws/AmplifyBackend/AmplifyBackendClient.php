<?php
namespace Aws\AmplifyBackend;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AmplifyBackend** service.
 * @method \Aws\Result cloneBackend(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cloneBackendAsync(array $args = [])
 * @method \Aws\Result createBackend(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBackendAsync(array $args = [])
 * @method \Aws\Result createBackendAPI(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBackendAPIAsync(array $args = [])
 * @method \Aws\Result createBackendAuth(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBackendAuthAsync(array $args = [])
 * @method \Aws\Result createBackendConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBackendConfigAsync(array $args = [])
 * @method \Aws\Result createToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTokenAsync(array $args = [])
 * @method \Aws\Result deleteBackend(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBackendAsync(array $args = [])
 * @method \Aws\Result deleteBackendAPI(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBackendAPIAsync(array $args = [])
 * @method \Aws\Result deleteBackendAuth(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBackendAuthAsync(array $args = [])
 * @method \Aws\Result deleteToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTokenAsync(array $args = [])
 * @method \Aws\Result generateBackendAPIModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise generateBackendAPIModelsAsync(array $args = [])
 * @method \Aws\Result getBackend(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBackendAsync(array $args = [])
 * @method \Aws\Result getBackendAPI(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBackendAPIAsync(array $args = [])
 * @method \Aws\Result getBackendAPIModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBackendAPIModelsAsync(array $args = [])
 * @method \Aws\Result getBackendAuth(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBackendAuthAsync(array $args = [])
 * @method \Aws\Result getBackendJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBackendJobAsync(array $args = [])
 * @method \Aws\Result getToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTokenAsync(array $args = [])
 * @method \Aws\Result listBackendJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBackendJobsAsync(array $args = [])
 * @method \Aws\Result removeAllBackends(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeAllBackendsAsync(array $args = [])
 * @method \Aws\Result removeBackendConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeBackendConfigAsync(array $args = [])
 * @method \Aws\Result updateBackendAPI(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBackendAPIAsync(array $args = [])
 * @method \Aws\Result updateBackendAuth(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBackendAuthAsync(array $args = [])
 * @method \Aws\Result updateBackendConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBackendConfigAsync(array $args = [])
 * @method \Aws\Result updateBackendJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBackendJobAsync(array $args = [])
 */
class AmplifyBackendClient extends AwsClient {}
