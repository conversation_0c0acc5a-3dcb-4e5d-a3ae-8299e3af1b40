<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AssistantApiCoreTypesCalendarEventRoomRoomLocationDetails extends \Google\Model
{
  /**
   * @var string
   */
  public $building;
  /**
   * @var string
   */
  public $city;
  /**
   * @var string
   */
  public $floor;
  public $latitude;
  public $longitude;
  /**
   * @var string
   */
  public $section;
  /**
   * @var string
   */
  public $simpleName;

  /**
   * @param string
   */
  public function setBuilding($building)
  {
    $this->building = $building;
  }
  /**
   * @return string
   */
  public function getBuilding()
  {
    return $this->building;
  }
  /**
   * @param string
   */
  public function setCity($city)
  {
    $this->city = $city;
  }
  /**
   * @return string
   */
  public function getCity()
  {
    return $this->city;
  }
  /**
   * @param string
   */
  public function setFloor($floor)
  {
    $this->floor = $floor;
  }
  /**
   * @return string
   */
  public function getFloor()
  {
    return $this->floor;
  }
  public function setLatitude($latitude)
  {
    $this->latitude = $latitude;
  }
  public function getLatitude()
  {
    return $this->latitude;
  }
  public function setLongitude($longitude)
  {
    $this->longitude = $longitude;
  }
  public function getLongitude()
  {
    return $this->longitude;
  }
  /**
   * @param string
   */
  public function setSection($section)
  {
    $this->section = $section;
  }
  /**
   * @return string
   */
  public function getSection()
  {
    return $this->section;
  }
  /**
   * @param string
   */
  public function setSimpleName($simpleName)
  {
    $this->simpleName = $simpleName;
  }
  /**
   * @return string
   */
  public function getSimpleName()
  {
    return $this->simpleName;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AssistantApiCoreTypesCalendarEventRoomRoomLocationDetails::class, 'Google_Service_Contentwarehouse_AssistantApiCoreTypesCalendarEventRoomRoomLocationDetails');
