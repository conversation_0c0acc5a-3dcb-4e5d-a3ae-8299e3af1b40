<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AppsPeopleOzExternalMergedpeopleapiStructuredPhone extends \Google\Model
{
  /**
   * @var string
   */
  public $formattedType;
  protected $phoneNumberType = AppsPeopleOzExternalMergedpeopleapiStructuredPhonePhoneNumber::class;
  protected $phoneNumberDataType = '';
  protected $shortCodeType = AppsPeopleOzExternalMergedpeopleapiStructuredPhoneShortCode::class;
  protected $shortCodeDataType = '';
  /**
   * @var string
   */
  public $type;

  /**
   * @param string
   */
  public function setFormattedType($formattedType)
  {
    $this->formattedType = $formattedType;
  }
  /**
   * @return string
   */
  public function getFormattedType()
  {
    return $this->formattedType;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiStructuredPhonePhoneNumber
   */
  public function setPhoneNumber(AppsPeopleOzExternalMergedpeopleapiStructuredPhonePhoneNumber $phoneNumber)
  {
    $this->phoneNumber = $phoneNumber;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiStructuredPhonePhoneNumber
   */
  public function getPhoneNumber()
  {
    return $this->phoneNumber;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiStructuredPhoneShortCode
   */
  public function setShortCode(AppsPeopleOzExternalMergedpeopleapiStructuredPhoneShortCode $shortCode)
  {
    $this->shortCode = $shortCode;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiStructuredPhoneShortCode
   */
  public function getShortCode()
  {
    return $this->shortCode;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsPeopleOzExternalMergedpeopleapiStructuredPhone::class, 'Google_Service_Contentwarehouse_AppsPeopleOzExternalMergedpeopleapiStructuredPhone');
