<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AppsPeopleOzExternalMergedpeopleapiMapsProfile extends \Google\Collection
{
  protected $collection_key = 'fieldRestriction';
  protected $fieldRestrictionType = AppsPeopleOzExternalMergedpeopleapiMapsProfileFieldRestriction::class;
  protected $fieldRestrictionDataType = 'array';
  protected $metadataType = AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata::class;
  protected $metadataDataType = '';
  /**
   * @var string
   */
  public $tagline;
  protected $websiteLinkType = AppsPeopleOzExternalMergedpeopleapiMapsProfileUrlLink::class;
  protected $websiteLinkDataType = '';

  /**
   * @param AppsPeopleOzExternalMergedpeopleapiMapsProfileFieldRestriction[]
   */
  public function setFieldRestriction($fieldRestriction)
  {
    $this->fieldRestriction = $fieldRestriction;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiMapsProfileFieldRestriction[]
   */
  public function getFieldRestriction()
  {
    return $this->fieldRestriction;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata
   */
  public function setMetadata(AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata $metadata)
  {
    $this->metadata = $metadata;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata
   */
  public function getMetadata()
  {
    return $this->metadata;
  }
  /**
   * @param string
   */
  public function setTagline($tagline)
  {
    $this->tagline = $tagline;
  }
  /**
   * @return string
   */
  public function getTagline()
  {
    return $this->tagline;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiMapsProfileUrlLink
   */
  public function setWebsiteLink(AppsPeopleOzExternalMergedpeopleapiMapsProfileUrlLink $websiteLink)
  {
    $this->websiteLink = $websiteLink;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiMapsProfileUrlLink
   */
  public function getWebsiteLink()
  {
    return $this->websiteLink;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsPeopleOzExternalMergedpeopleapiMapsProfile::class, 'Google_Service_Contentwarehouse_AppsPeopleOzExternalMergedpeopleapiMapsProfile');
