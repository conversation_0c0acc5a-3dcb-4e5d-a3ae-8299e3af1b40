<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AppsPeopleOzExternalMergedpeopleapiPlaceDetails extends \Google\Model
{
  /**
   * @var string
   */
  public $googleUrl;
  protected $latLngType = AppsPeopleOzExternalMergedpeopleapiLatLng::class;
  protected $latLngDataType = '';
  protected $metadataType = AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata::class;
  protected $metadataDataType = '';
  protected $openingHoursType = AppsPeopleOzExternalMergedpeopleapiOpeningHours::class;
  protected $openingHoursDataType = '';
  /**
   * @var string
   */
  public $primaryTypeName;

  /**
   * @param string
   */
  public function setGoogleUrl($googleUrl)
  {
    $this->googleUrl = $googleUrl;
  }
  /**
   * @return string
   */
  public function getGoogleUrl()
  {
    return $this->googleUrl;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiLatLng
   */
  public function setLatLng(AppsPeopleOzExternalMergedpeopleapiLatLng $latLng)
  {
    $this->latLng = $latLng;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiLatLng
   */
  public function getLatLng()
  {
    return $this->latLng;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata
   */
  public function setMetadata(AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata $metadata)
  {
    $this->metadata = $metadata;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiPersonFieldMetadata
   */
  public function getMetadata()
  {
    return $this->metadata;
  }
  /**
   * @param AppsPeopleOzExternalMergedpeopleapiOpeningHours
   */
  public function setOpeningHours(AppsPeopleOzExternalMergedpeopleapiOpeningHours $openingHours)
  {
    $this->openingHours = $openingHours;
  }
  /**
   * @return AppsPeopleOzExternalMergedpeopleapiOpeningHours
   */
  public function getOpeningHours()
  {
    return $this->openingHours;
  }
  /**
   * @param string
   */
  public function setPrimaryTypeName($primaryTypeName)
  {
    $this->primaryTypeName = $primaryTypeName;
  }
  /**
   * @return string
   */
  public function getPrimaryTypeName()
  {
    return $this->primaryTypeName;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsPeopleOzExternalMergedpeopleapiPlaceDetails::class, 'Google_Service_Contentwarehouse_AppsPeopleOzExternalMergedpeopleapiPlaceDetails');
