<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AppsPeopleOzExternalMergedpeopleapiUserVisibleStats extends \Google\Model
{
  /**
   * @var string
   */
  public $incomingAnyCircleCount;
  /**
   * @var string
   */
  public $viewCount;

  /**
   * @param string
   */
  public function setIncomingAnyCircleCount($incomingAnyCircleCount)
  {
    $this->incomingAnyCircleCount = $incomingAnyCircleCount;
  }
  /**
   * @return string
   */
  public function getIncomingAnyCircleCount()
  {
    return $this->incomingAnyCircleCount;
  }
  /**
   * @param string
   */
  public function setViewCount($viewCount)
  {
    $this->viewCount = $viewCount;
  }
  /**
   * @return string
   */
  public function getViewCount()
  {
    return $this->viewCount;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsPeopleOzExternalMergedpeopleapiUserVisibleStats::class, 'Google_Service_Contentwarehouse_AppsPeopleOzExternalMergedpeopleapiUserVisibleStats');
