<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AssistantApiAndroidIntentCapabilitiesAndroidIntentCapability extends \Google\Collection
{
  protected $collection_key = 'packageNames';
  /**
   * @var string
   */
  public $intentActionName;
  /**
   * @var string[]
   */
  public $packageNames;

  /**
   * @param string
   */
  public function setIntentActionName($intentActionName)
  {
    $this->intentActionName = $intentActionName;
  }
  /**
   * @return string
   */
  public function getIntentActionName()
  {
    return $this->intentActionName;
  }
  /**
   * @param string[]
   */
  public function setPackageNames($packageNames)
  {
    $this->packageNames = $packageNames;
  }
  /**
   * @return string[]
   */
  public function getPackageNames()
  {
    return $this->packageNames;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AssistantApiAndroidIntentCapabilitiesAndroidIntentCapability::class, 'Google_Service_Contentwarehouse_AssistantApiAndroidIntentCapabilitiesAndroidIntentCapability');
