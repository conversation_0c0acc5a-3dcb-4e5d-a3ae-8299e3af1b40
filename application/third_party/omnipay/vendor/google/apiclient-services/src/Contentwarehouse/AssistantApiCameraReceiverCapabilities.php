<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class AssistantApiCameraReceiverCapabilities extends \Google\Collection
{
  protected $collection_key = 'supportedCameraReceivers';
  /**
   * @var bool
   */
  public $hasLimitedCameraStreamCapability;
  protected $supportedCameraReceiversType = AssistantApiCoreTypesCastAppInfo::class;
  protected $supportedCameraReceiversDataType = 'array';

  /**
   * @param bool
   */
  public function setHasLimitedCameraStreamCapability($hasLimitedCameraStreamCapability)
  {
    $this->hasLimitedCameraStreamCapability = $hasLimitedCameraStreamCapability;
  }
  /**
   * @return bool
   */
  public function getHasLimitedCameraStreamCapability()
  {
    return $this->hasLimitedCameraStreamCapability;
  }
  /**
   * @param AssistantApiCoreTypesCastAppInfo[]
   */
  public function setSupportedCameraReceivers($supportedCameraReceivers)
  {
    $this->supportedCameraReceivers = $supportedCameraReceivers;
  }
  /**
   * @return AssistantApiCoreTypesCastAppInfo[]
   */
  public function getSupportedCameraReceivers()
  {
    return $this->supportedCameraReceivers;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AssistantApiCameraReceiverCapabilities::class, 'Google_Service_Contentwarehouse_AssistantApiCameraReceiverCapabilities');
