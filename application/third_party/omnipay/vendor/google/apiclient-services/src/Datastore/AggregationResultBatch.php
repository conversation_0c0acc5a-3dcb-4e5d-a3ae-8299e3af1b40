<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Datastore;

class AggregationResultBatch extends \Google\Collection
{
  protected $collection_key = 'aggregationResults';
  protected $aggregationResultsType = AggregationResult::class;
  protected $aggregationResultsDataType = 'array';
  /**
   * @var string
   */
  public $moreResults;
  /**
   * @var string
   */
  public $readTime;

  /**
   * @param AggregationResult[]
   */
  public function setAggregationResults($aggregationResults)
  {
    $this->aggregationResults = $aggregationResults;
  }
  /**
   * @return AggregationResult[]
   */
  public function getAggregationResults()
  {
    return $this->aggregationResults;
  }
  /**
   * @param string
   */
  public function setMoreResults($moreResults)
  {
    $this->moreResults = $moreResults;
  }
  /**
   * @return string
   */
  public function getMoreResults()
  {
    return $this->moreResults;
  }
  /**
   * @param string
   */
  public function setReadTime($readTime)
  {
    $this->readTime = $readTime;
  }
  /**
   * @return string
   */
  public function getReadTime()
  {
    return $this->readTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AggregationResultBatch::class, 'Google_Service_Datastore_AggregationResultBatch');
