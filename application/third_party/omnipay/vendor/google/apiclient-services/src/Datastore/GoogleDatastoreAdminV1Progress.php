<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Datastore;

class GoogleDatastoreAdminV1Progress extends \Google\Model
{
  /**
   * @var string
   */
  public $workCompleted;
  /**
   * @var string
   */
  public $workEstimated;

  /**
   * @param string
   */
  public function setWorkCompleted($workCompleted)
  {
    $this->workCompleted = $workCompleted;
  }
  /**
   * @return string
   */
  public function getWorkCompleted()
  {
    return $this->workCompleted;
  }
  /**
   * @param string
   */
  public function setWorkEstimated($workEstimated)
  {
    $this->workEstimated = $workEstimated;
  }
  /**
   * @return string
   */
  public function getWorkEstimated()
  {
    return $this->workEstimated;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleDatastoreAdminV1Progress::class, 'Google_Service_Datastore_GoogleDatastoreAdminV1Progress');
