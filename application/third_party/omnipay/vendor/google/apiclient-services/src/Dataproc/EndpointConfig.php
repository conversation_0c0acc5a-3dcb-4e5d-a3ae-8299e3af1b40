<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dataproc;

class EndpointConfig extends \Google\Model
{
  /**
   * @var bool
   */
  public $enableHttpPortAccess;
  /**
   * @var string[]
   */
  public $httpPorts;

  /**
   * @param bool
   */
  public function setEnableHttpPortAccess($enableHttpPortAccess)
  {
    $this->enableHttpPortAccess = $enableHttpPortAccess;
  }
  /**
   * @return bool
   */
  public function getEnableHttpPortAccess()
  {
    return $this->enableHttpPortAccess;
  }
  /**
   * @param string[]
   */
  public function setHttpPorts($httpPorts)
  {
    $this->httpPorts = $httpPorts;
  }
  /**
   * @return string[]
   */
  public function getHttpPorts()
  {
    return $this->httpPorts;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(EndpointConfig::class, 'Google_Service_Dataproc_EndpointConfig');
