<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Paypost extends CI_Controller
{
    
    
    function payencrypt( $data = '' ) {
		$cipher = 'AES-128-ECB';
		$key = "3867841532201700";
		$encrypted = openssl_encrypt( $data, $cipher, $key, OPENSSL_RAW_DATA );
		return base64_encode( $encrypted );
	}
    
    public function success(){
        if ($this->input->server('REQUEST_METHOD') == "POST") {
            
            $reference_no    = substr(rand( 1000000000, 9999999999 ).'_4589', -15 );
		    $amount          = 10;
		    $sub_merchant_id = 45;
		    $paymode         = 9;
		    
		    $Upivpa = 'yes';
		    $orderId = time();
		    $name = 'HMT';
		    
		    $return_url = 'https://erp.sxcjpr.edu.in';
		    //$return_url = base_url('paypost/dod');
		    
		    $billing_address = 'jaipur Rajasthan';
    		$customer_phone  = '**********';
    		
    		$mandatory_fields = $reference_no.'|'.$sub_merchant_id.'|'.$amount.'|'.$Upivpa.'|'.$orderId;
		    $optional_fields  = $name;
    		    
		    $baseUrl = 'https://eazypay.icicibank.com/EazyPG?';
    		
    		$url_param     = [
			    'merchantid'         => '383220',
			    'mandatory fields'   => $this->payencrypt( $mandatory_fields ),
			    'optional fields'    => $this->payencrypt( $optional_fields ),
			    'returnurl'          => $this->payencrypt( $return_url ),
			    'Reference No'       => $this->payencrypt( $reference_no ),
			    'submerchantid'      => $this->payencrypt( $sub_merchant_id ),
			    'transaction amount' => $this->payencrypt( $amount ),
			    'paymode'            => $this->payencrypt( $paymode ),
		    ];
		    $url  = $baseUrl.http_build_query( $url_param );
            redirect($url);
            exit;
        }
        $this->load->view('paypost');
    }
    
    function dod(){
        die("fsdfsdfsd");
    }
}