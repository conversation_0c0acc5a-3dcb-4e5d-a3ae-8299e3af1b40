<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Eazypay extends OnlineAdmission_Controller
{

    public $pay_method = "";
    public $amount = 0;

    function __construct() {
        parent::__construct();
        $this->pay_method = $this->paymentsetting_model->getActiveMethod();
        $this->setting = $this->setting_model->getSetting();
        $this->amount = $this->setting->online_admission_amount;
        $this->load->library('mailsmsconf');
        $this->load->model('onlinestudent_model');
    }

    public function index_old()
    {
        $this->session->set_userdata('payment_amount',$this->amount);
        $reference = $this->session->userdata('reference');
        $txnid                      = substr(hash('sha256', mt_rand() . microtime()), 0, 20);
        $online_data = $this->onlinestudent_model->getAdmissionData($reference);
        //eazypaymoney details
        $amount           = convertBaseAmountCurrencyFormat($this->amount);
        $customer_name    = $online_data->firstname." ".$online_data->lastname;
        $customer_emial   = $online_data->email;
        $customer_mobile  = $online_data->mobileno;
        $customer_address  = $online_data->current_address;
        $product_info = 'Online Fees Payment';
        $MERCHANT_KEY = $this->pay_method->api_secret_key;
        $SALT         = $this->pay_method->salt;

        //optional udf values
        $udf1 = '';
        $udf2 = '';
        $udf3 = ''; 
        $udf4 = '';
        $udf5 = '';

        $hashstring = $MERCHANT_KEY . '|' . $txnid . '|' . $amount . '|' . $product_info . '|' . $customer_name . '|' . $customer_emial . '|' . $udf1 . '|' . $udf2 . '|' . $udf3 . '|' . $udf4 . '|' . $udf5 . '||||||' . $SALT;
        $hash       = strtolower(hash('sha512', $hashstring));

        $success = base_url('onlineadmission/eazypay/success');
        $fail    = base_url('onlineadmission/eazypay/success');
        $cancel  = base_url('onlineadmission/eazypay/success');
        $data    = array(
            'mkey'                      => $MERCHANT_KEY,
            'tid'                       => $txnid,
            'hash'                      => $hash,
            'amount'                    => ($this->amount),
            'name'                      => $customer_name,
            'productinfo'               => $product_info,
            'mailid'                    => $customer_emial,
            'phoneno'                   => $customer_mobile,
            'address'                   => $customer_address,
            'action'                    => "https://eazypay.icicibank.com/EazyPG?", 
            'sucess'                    => $success,
            'failure'                   => $fail,
            'cancel'                    => $cancel,
        );
        $data['setting']      = $this->setting;
      
        $this->load->view('onlineadmission/eazypay/index', $data);
    }
    
    
    function index(){

        $pre_session_data           = $this->session->userdata('validlogin');
        $query = $this->db->get_where('online_admissions', ['reference_no' => $pre_session_data], 1);
        $studentData = (array)$query->row();
        if(!empty($studentData)){
            $query1 = $this->db->limit(1)->get('sch_settings');
            $settings = (array) $query1->row(); 

            $st_id = $pre_session_data;
            $txnid = 's_'.rand( 100000000000000000, 999999999999999999 );
            $reference_no = $txnid;  
            $session_data['name']           =  $studentData['firstname'];
            $session_data['email']          = ($studentData['email'] != "") ? $studentData['email'] : "<EMAIL>";
            $session_data['guardian_phone'] =  "";
            $session_data['address']        = "";
            $pay_method                     = $this->paymentsetting_model->getActiveMethod();
        
            $amount  = round(number_format((float) (convertBaseAmountCurrencyFormat($settings['online_admission_amount'])), 2, '.', ''));
            
            $customer_name    = $session_data['name'];
            $customer_emial   = $session_data['email'];
            $customer_mobile  = $session_data['guardian_phone'];
            $customer_address = $session_data['address'];

            $product_info = 'Online Admission Payment';
            $MERCHANT_KEY = $pay_method->api_secret_key;
            $SALT         = $pay_method->salt;
            
            $success    = base_url('onlineadmission/eazypay/success');
            $fail    = base_url('onlineadmission/eazypay/success');
            $cancel  = base_url('onlineadmission/eazypay/success');
            
            $hash  = "";
            
            $submerchantid = $pre_session_data;
            $hashstring = $txnid.'|'.$submerchantid.'|'.$amount;
            $optionalstring = $customer_mobile.'|'.$amount;
            
            $sub_merchant_id = $submerchantid;
            $paymode         = 9;
            
            $Upivpa = 'yes';
            $orderId = time();
            
            $return_url = $success;
            
            $mandatory_fields = $reference_no.'|'.$sub_merchant_id.'|'.$amount.'|'.$Upivpa.'|'.$orderId;
            $optional_fields  = $customer_name;

            $baseUrl = 'https://eazypay.icicibank.com/EazyPG?';
                
            $url_param     = [
                'merchantid'         => '383220',
                'mandatory fields'   => $this->payencrypt( $mandatory_fields ),
                'optional fields'    => $this->payencrypt( $optional_fields ),
                'returnurl'          => $this->payencrypt( $return_url ),
                'Reference No'       => $this->payencrypt( $reference_no ),
                'submerchantid'      => $this->payencrypt( $sub_merchant_id ),
                'transaction amount' => $this->payencrypt( $amount ),
                'paymode'            => $this->payencrypt( $paymode ),
            ];

            $nurl_param     = [
                'merchantid'         => '383220',
                'mandatory fields'   => $mandatory_fields,
                'optional fields'    => $optional_fields,
                'returnurl'          => $return_url,
                'Reference No'       => $reference_no,
                'submerchantid'      => $sub_merchant_id,
                'transaction amount' => $amount,
                'paymode'            => $paymode,
            ];

            $encryptedUrl  = $baseUrl.http_build_query( $url_param );
            //prd($encryptedUrl);
            $data    = array(
                'mkey'                      => $MERCHANT_KEY,
                'tid'                       => $txnid,
                'hash'                      => $hash,
                'amount'                    => $amount,
                'student_fees_master_array' => [],
                'name'                      => $customer_name,
                'productinfo'               => $product_info,
                'mailid'                    => $customer_emial,
                'phoneno'                   => $customer_mobile,
                'address'                   => $customer_address,
                'action'                    => $encryptedUrl, //for live change action  https://eazypay.icicibank.com/EazyPG?
                'sucess'                    => $success,
                'failure'                   => $fail,
                'cancel'                    => $cancel,
            );
            $data['session_data'] = $session_data;
            $data['setting']      = $this->setting;

            //prd($data['action']);

            $this->load->view('onlineadmission/eazypay/index', $data);
        }else{
            die("something went sorry");
        }
    }

    function payencrypt( $data = '' ) {
        $cipher = 'AES-128-ECB';
        $pay_method = $this->paymentsetting_model->getActiveMethod();
        $key = $pay_method->salt;
        $encrypted = openssl_encrypt( $data, $cipher, $key, OPENSSL_RAW_DATA );
        return base64_encode( $encrypted );
    }

    public function checkout()
    {

        $this->form_validation->set_rules('firstname', $this->lang->line('customer_name'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('phone', $this->lang->line('mobile_number'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('email', $this->lang->line('email'), 'required|valid_email|trim|xss_clean');
        $this->form_validation->set_rules('amount', $this->lang->line('amount'), 'required|trim|xss_clean');

        if ($this->form_validation->run() == false) {
            $data = array(
                'firstname' => form_error('firstname'),
                'phone'     => form_error('phone'),
                'email'     => form_error('email'),
                'amount'    => form_error('amount'),
            );
            $array = array('status' => 'fail', 'error' => $data);
            echo json_encode($array);
        } else {

            $array = array('status' => 'success', 'error' => '');
            echo json_encode($array);
        }
    }

    public function success(){
	
        if ($this->input->server('REQUEST_METHOD') == 'POST') {
          
            $amount = $this->amount;
            if ($this->input->post('Response_Code') == "E000") {
                	$reference  = $this->input->post('SubMerchantId');
                    $online_data = $this->onlinestudent_model->getAdmissionDataByRef($reference);
               // echo "<pre>";
                //print_r($_POST);
                //die;
                $mihpayid      = $this->input->post('mihpayid');
                $transactionid = $this->input->post('Unique_Ref_Number');
                if (!empty($transactionid)) {
                    
                    $currentdate = date('Y-m-d');
                    $adddata = array('id' => $reference, 'form_status' => 1, 'submit_date' => $currentdate);
                    $this->onlinestudent_model->edit($adddata);
                    
                    $apply_date=date("Y-m-d H:i:s");
                    
                    $date         = date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat(date("Y-m-d", strtotime($apply_date))));
                    
                    $gateway_response['online_admission_id']   = $online_data->id; 
                    $gateway_response['paid_amount']    = $amount;
                    $gateway_response['transaction_id'] = $transactionid;
                    $gateway_response['payment_mode']   = 'eazypay';
                    $gateway_response['payment_type']   = 'online';
                    $gateway_response['note']           = $this->lang->line('online_fees_deposit_through_eazypay_txn_id'). $transactionid;
                    $gateway_response['date']           = date("Y-m-d H:i:s");
                    $return_detail                      = $this->onlinestudent_model->paymentSuccess($gateway_response);
                     $sender_details = array('firstname' => $online_data->firstname, 'lastname' => $online_data->lastname, 'email' => $online_data->email,'date'=>$date,'reference_no'=>$online_data->reference_no,'mobileno'=>$online_data->mobileno,'paid_amount'=>$this->amount,'guardian_email'=>$online_data->guardian_email,'guardian_phone'=>$online_data->guardian_phone);
                    $this->mailsmsconf->mailsms('online_admission_fees_submission', $sender_details);
                    redirect(base_url("onlineadmission/checkout/successinvoice/".$online_data->reference_no));
                } else {
                    redirect(base_url("onlineadmission/checkout/paymentfailed/".$online_data->reference_no));
                }
            }else {
                redirect(base_url("onlineadmission/checkout/paymentfailed/".$online_data->reference_no));
            }
        }
    }

}