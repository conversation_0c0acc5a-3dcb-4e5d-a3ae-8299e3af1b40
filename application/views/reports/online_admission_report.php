<div class="content-wrapper" style="min-height: 946px;">
    <!-- Main content -->
    <section class="content">
        <?php $this->load->view('reports/_studentinformation'); ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box removeboxmius">
                    <div class="box-header ptbnull"></div>
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form role="form" action="<?php echo site_url('report/checkvalidation') ?>" method="post" id="reportform" >
                            <div class="row">
                                <?php echo $this->customlib->getCSRF(); ?>
                                <div class="col-sm-4 col-md-4">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('class'); ?></label>
                                        <select autofocus="" id="class_id" name="class_id" class="form-control" >
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                            <?php
                                            foreach ($classlist as $class) {
                                                ?>
                                                <option value="<?php echo $class['id'] ?>" <?php if (set_value('class_id') == $class['id']) echo "selected=selected" ?> ><?php echo $class['class'] ?></option>
                                                <?php
                                                $count++;
                                            }
                                            ?>
                                        </select>
                                      <span class="text-danger" id="error_class_id"></span>
                                    </div>
                                </div>
                                <div class="col-sm-4 col-md-3 ">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('section'); ?></label>
                                        <select autofocus="" id="section_id" name="section_id" class="form-control" >
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4 col-md-2 ">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('status'); ?></label>
                                        <select autofocus="" id="status" name="status" class="form-control" >
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                            <option value="0"><?php echo $this->lang->line('pending'); ?></option>
                                            <option value="1"><?php echo $this->lang->line('admitted'); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4 col-md-3">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('search_by_admission_date'); ?></label>
                                        <select class="form-control" name="search_type" onchange="showdate(this.value)">
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                            <option value="today" <?php if (isset($search_type) && $search_type == 'today') echo "selected"; ?>><?php echo $this->lang->line('today'); ?></option>
                                            <option value="yesterday" <?php if (isset($search_type) && $search_type == 'yesterday') echo "selected"; ?>><?php echo $this->lang->line('yesterday'); ?></option>
                                            <option value="this_week" <?php if (isset($search_type) && $search_type == 'this_week') echo "selected"; ?>><?php echo $this->lang->line('this_week'); ?></option>
                                            <option value="last_week" <?php if (isset($search_type) && $search_type == 'last_week') echo "selected"; ?>><?php echo $this->lang->line('last_week'); ?></option>
                                            <option value="this_month" <?php if (isset($search_type) && $search_type == 'this_month') echo "selected"; ?>><?php echo $this->lang->line('this_month'); ?></option>
                                            <option value="last_month" <?php if (isset($search_type) && $search_type == 'last_month') echo "selected"; ?>><?php echo $this->lang->line('last_month'); ?></option>
                                            <option value="last_3_month" <?php if (isset($search_type) && $search_type == 'last_3_month') echo "selected"; ?>><?php echo $this->lang->line('last_3_month'); ?></option>
                                            <option value="last_6_month" <?php if (isset($search_type) && $search_type == 'last_6_month') echo "selected"; ?>><?php echo $this->lang->line('last_6_month'); ?></option>
                                            <option value="last_12_month" <?php if (isset($search_type) && $search_type == 'last_12_month') echo "selected"; ?>><?php echo $this->lang->line('last_12_month'); ?></option>
                                            <option value="period" <?php if (isset($search_type) && $search_type == 'period') echo "selected"; ?>><?php echo $this->lang->line('period'); ?></option>
                                        </select>
                                        <span class="text-danger"><?php echo form_error('search_type'); ?></span>
                                    </div>
                                </div>
                                <div id="date_result" class="col-sm-12 col-md-12"></div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <button type="submit" name="search" value="search_filter" class="btn btn-primary btn-sm checkbox-toggle pull-right"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                                    </div>
                                </div>
                            </div><!--./row-->
                        </form>
                    </div><!--./box-body-->
                    <div class="box-header ptbnull"></div>
                    <div class="">
                        <div class="box-header ptbnull">
                            <h3 class="box-title titlefix"><i class="fa fa-users"></i> <?php echo $this->lang->line('online_admission_report'); ?></h3>
                        </div>
                        <div class="box-body table-responsive">
                           <!-- Loading overlay -->
                           <div id="loading-overlay" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.8); z-index: 1000; text-align: center; padding-top: 50px;">
                               <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
                               <br><br>
                               <strong>Loading student data, please wait...</strong>
                           </div>
                           <table class="table table-striped table-bordered table-hover student-list" data-export-title="<?php echo $this->lang->line('online_admission_report'); ?>" style="width:100%;">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('admission_date'); ?></th>
                                        <th><?php echo $this->lang->line('reference_no'); ?></th>
                                        <th><?php echo $this->lang->line('admission_no'); ?></th>
                                        <th><?php echo $this->lang->line('student_name'); ?></th>
                                        <th><?php echo $this->lang->line('class'); ?></th>
                                        <th><?php echo $this->lang->line('mobile_number'); ?></th>
                                        <th><?php echo $this->lang->line('date_of_birth'); ?></th>
                                        <th><?php echo $this->lang->line('gender'); ?></th>
                                        <th><?php echo $this->lang->line('email'); ?></th>
                                        <th><?php echo $this->lang->line('category'); ?></th>
                                        <th><?php echo $this->lang->line('religion'); ?></th>
                                        <th><?php echo $this->lang->line('blood_group'); ?></th>
                                        <th><?php echo $this->lang->line('national_identification_number'); ?></th>
                                        <th><?php echo $this->lang->line('father_name'); ?></th>
                                        <th><?php echo $this->lang->line('father_occupation'); ?></th>
                                        <th><?php echo $this->lang->line('father_phone'); ?></th>
                                        <th><?php echo $this->lang->line('mother_name'); ?></th>
                                        <th><?php echo $this->lang->line('mother_occupation'); ?></th>
                                        <th><?php echo $this->lang->line('mother_phone'); ?></th>
                                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                                        <th><?php echo $this->lang->line('guardian_relation'); ?></th>
                                        <th><?php echo $this->lang->line('guardian_occupation'); ?></th>
                                        <th><?php echo $this->lang->line('guardian_phone'); ?></th>
                                        <th><?php echo $this->lang->line('guardian_email'); ?></th>
                                        <?php
                                        // Add custom fields headers
                                        $custom_fields = $this->customfield_model->get_custom_fields('students');
                                        if (!empty($custom_fields)) {
                                            foreach ($custom_fields as $custom_field) {
                                                if ($this->customlib->getfieldstatus($custom_field->name)) {
                                                    ?>
                                                    <th><?php echo $custom_field->name; ?></th>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>
                                        <th>Student Photo</th>
                                        <th>Student Signature</th>
                                        <th>Class 10th Marksheet</th>
                                        <th>Class 12th Marksheet</th>
                                        <th><?php echo $this->lang->line('form_status'); ?></th>
                                        <th><?php echo $this->lang->line('payment_status'); ?></th>
                                        <th><?php echo $this->lang->line('enrolled'); ?></th>
                                        <th><?php echo $this->lang->line('amount'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!--./box box-primary-->
            </div><!--./col-md-12-->
        </div>
    </div>
</section>
</div>

<script type="text/javascript">
    function getSectionByClass(class_id, section_id) {
        if (class_id != "" && section_id != "") {
            $('#section_id').html("");
            var base_url = '<?php echo base_url() ?>';
            var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
            $.ajax({
                type: "GET",
                url: base_url + "sections/getByClass",
                data: {'class_id': class_id},
                dataType: "json",
                success: function (data) {
                    $.each(data, function (i, obj)
                    {
                        var sel = "";
                        if (section_id == obj.id) {
                            sel = "selected";
                        }
                        div_data += "<option value=" + obj.section_id + " " + sel + ">" + obj.section + "</option>";
                    });
                    $('#section_id').append(div_data);
                }
            });
        }
    }

    $(document).ready(function () {
        var class_id = $('#class_id').val();
        var section_id = '<?php echo set_value('section_id') ?>';
        getSectionByClass(class_id, section_id);
        $(document).on('change', '#class_id', function (e) {
            $('#section_id').html("");
            var class_id = $(this).val();
            var base_url = '<?php echo base_url() ?>';
            var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
            $.ajax({
                type: "GET",
                url: base_url + "sections/getByClass",
                data: {'class_id': class_id},
                dataType: "json",
                success: function (data) {
                    $.each(data, function (i, obj)
                    {
                        div_data += "<option value=" + obj.section_id + ">" + obj.section + "</option>";
                    });
                    $('#section_id').append(div_data);
                }
            });
        });
    });
</script>

<script>
$(document).ready(function() {
     emptyDatatable('student-list','data');
});
</script>

<script type="text/javascript">
$(document).ready(function(){
    $('.date').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true
    });
    
$(document).on('submit','#reportform',function(e){
    e.preventDefault(); // avoid to execute the actual submit of the form.
    var $this = $(this).find("button[type=submit]:focus");
    var form = $(this);
    var url = form.attr('action');
    var form_data = form.serializeArray();
    $.ajax({
           url: url,
           type: "POST",
           dataType:'JSON',
           data: form_data, // serializes the form's elements.
              beforeSend: function () {
                $('[id^=error]').html("");
                $this.button('loading');
                $('#loading-overlay').show();
               },
              success: function(response) { // your success handler

                if(!response.status){
                    $.each(response.error, function(key, value) {
                    $('#error_' + key).html(value);
                    });
                }else{
                   console.log("Response params:", response.params);
                   // Use optimized pagination settings for large datasets
                   initDatatableOptimized('student-list','report/dtonlineadmissionreportlist',response.params);
                }
              },
             error: function() { // your error handler
                 $this.button('reset');
                 $('#loading-overlay').hide();
             },
             complete: function() {
               $this.button('reset');
               $('#loading-overlay').hide();
             }
         });
        });
    });

// Optimized DataTable function for large datasets
function initDatatableOptimized(selector, url, params) {
    if ($.fn.DataTable.isDataTable('.' + selector)) {
        $('.' + selector).DataTable().destroy();
    }

    $('.' + selector).DataTable({
        dom: '<"top"f><Bl>r<t>ip',
        lengthMenu: [[25, 50, 100, 250], [25, 50, 100, 250]], // Remove "All" option
        buttons: [
            {
                extend: 'copy',
                text: '<i class="fa fa-files-o"></i>',
                titleAttr: 'Copy',
                className: "btn-copy",
                title: $('.' + selector).data("exportTitle"),
                exportOptions: {
                    columns: ["thead th:not(.noExport)"]
                }
            },
            {
                extend: 'excel',
                text: '<i class="fa fa-file-excel-o"></i>',
                titleAttr: 'Excel (Current Page)',
                className: "btn-excel",
                title: $('.' + selector).data("exportTitle"),
                exportOptions: {
                    columns: ["thead th:not(.noExport)"]
                }
            },
            {
                text: '<i class="fa fa-download"></i> Export All',
                titleAttr: 'Export All Data to Excel',
                className: "btn-export-all btn btn-success",
                action: function (e, dt, node, config) {
                    exportAllToExcel(params);
                }
            },
            {
                extend: 'csv',
                text: '<i class="fa fa-file-text-o"></i>',
                titleAttr: 'CSV',
                className: "btn-csv",
                title: $('.' + selector).data("exportTitle"),
                exportOptions: {
                    columns: ["thead th:not(.noExport)"]
                }
            },
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i>',
                titleAttr: 'Print',
                className: "btn-print",
                title: $('.' + selector).data("exportTitle"),
                customize: function (win) {
                    $(win.document.body).find('th').addClass('display').css('text-align', 'center');
                    $(win.document.body).find('table').addClass('display').css('font-size', '14px');
                    $(win.document.body).find('td').addClass('display').css('text-align', 'left');
                    $(win.document.body).find('h1').css('text-align', 'center');
                },
                exportOptions: {
                    columns: ["thead th:not(.noExport)"]
                }
            }
        ],
        language: {
            processing: '<i class="fa fa-spinner fa-spin fa-2x fa-fw"></i><br><span class="sr-only">Loading...</span><strong>Loading data, please wait...</strong>',
            sLengthMenu: "_MENU_"
        },
        pageLength: 50, // Start with smaller page size
        searching: true,
        aaSorting: [],
        aoColumnDefs: [{ "bSortable": false, "aTargets": [-1], 'sClass': 'dt-body-right' }],
        processing: true,
        serverSide: true,
        deferRender: true, // Improve performance for large datasets
        ajax: {
            url: baseurl + url,
            dataSrc: "data",
            type: "POST",
            data: params,
            error: function(xhr, error, thrown) {
                console.error('DataTable Ajax Error:', error, thrown);
                alert('Error loading data. Please try again.');
            }
        }
    });
}

// Function to export all data to Excel
function exportAllToExcel(params) {
    // Show loading indicator
    var exportBtn = $('.btn-export-all');
    var originalText = exportBtn.html();
    exportBtn.html('<i class="fa fa-spinner fa-spin"></i> Exporting...').prop('disabled', true);

    // Create form and submit to export endpoint
    var form = $('<form>', {
        'method': 'POST',
        'action': baseurl + 'report/exportonlineadmissionreport'
    });

    // Add parameters
    $.each(params, function(key, value) {
        form.append($('<input>', {
            'type': 'hidden',
            'name': key,
            'value': value
        }));
    });

    // Add CSRF token
    form.append($('<input>', {
        'type': 'hidden',
        'name': 'csrf_test_name',
        'value': $('[name="csrf_test_name"]').val()
    }));

    // Submit form
    form.appendTo('body').submit().remove();

    // Reset button after delay
    setTimeout(function() {
        exportBtn.html(originalText).prop('disabled', false);
    }, 3000);
}

function showdate(value) {
    if (value == 'period') {
        $('#date_result').html('<div class="row"><div class="col-md-6"><div class="form-group"><label><?php echo $this->lang->line("date_from"); ?></label><input type="text" id="date_from" name="date_from" class="form-control date"></div></div><div class="col-md-6"><div class="form-group"><label><?php echo $this->lang->line("date_to"); ?></label><input type="text" id="date_to" name="date_to" class="form-control date"></div></div></div>');
        
        // Initialize the datepicker after adding the fields to the DOM
        $('.date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });
    } else {
        $('#date_result').html("");
    }
}
</script>