<?php

defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = '{field} field is required';
$lang['form_validation_isset'] = '{field} field must have a value';
$lang['form_validation_valid_email'] = '{field} field must contain a valid email address';
$lang['form_validation_valid_emails'] = '{field} field must contain all valid email addresses';
$lang['form_validation_valid_url'] = '{field} field must contain a valid URL';
$lang['form_validation_valid_ip'] = '{field} field must contain a valid IP';
$lang['form_validation_min_length'] = '{field} field must be at least {param} characters in length';
$lang['form_validation_max_length'] = '{field} field cannot exceed {param} characters in length';
$lang['form_validation_exact_length'] = '{field} field must be exactly {param} characters in length';
$lang['form_validation_alpha'] = '{field} field may only contain alphabetical characters';
$lang['form_validation_alpha_numeric'] = '{field} field may only contain alpha-numeric characters';
$lang['form_validation_alpha_numeric_spaces'] = '{field} field may only contain alpha-numeric characters and spaces';
$lang['form_validation_alpha_dash'] = '{field} field may only contain alpha-numeric characters, underscores, and dashes';
$lang['form_validation_numeric'] = '{field} field must contain only numbers';
$lang['form_validation_is_numeric'] = '{field} field must contain only numeric characters';
$lang['form_validation_integer'] = '{field} field must contain an integer';
$lang['form_validation_regex_match'] = '{field} field is not in the correct format';
$lang['form_validation_matches'] = '{field} field does not match the {param} field';
$lang['form_validation_differs'] = '{field} field must differ from the {param} field';
$lang['form_validation_is_unique'] = '{field} field must contain a unique value';
$lang['form_validation_is_natural'] = '{field} field must only contain digits';
$lang['form_validation_is_natural_no_zero'] = '{field} field must only contain digits and must be greater than zero';
$lang['form_validation_decimal'] = '{field} field must contain a decimal number';
$lang['form_validation_less_than'] = '{field} field must contain a number less than {param}';
$lang['form_validation_less_than_equal_to'] = '{field} field must contain a number less than or equal to {param}';
$lang['form_validation_greater_than'] = '{field} field must contain a number greater than {param}';
$lang['form_validation_greater_than_equal_to'] = '{field} field must contain a number greater than or equal to {param}';
$lang['form_validation_error_message_not_set'] = 'Unable to access an error message corresponding to your field name {field}';
$lang['form_validation_in_list'] = '{field} field must be one of: {param}';
